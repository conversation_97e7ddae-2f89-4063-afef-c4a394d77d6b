.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-layout {
  flex: 1;
  display: flex;
}

.sidebar {
  width: 250px;
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.header {
  height: 64px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  padding: 0 24px;
}

.main-content {
  flex: 1;
  padding: 16px;
  background: #f5f5f5;
}
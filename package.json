{"name": "email-manager", "version": "1.0.0", "description": "A modern web-based email management application", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "vite", "dev:server": "nodemon server/src/index.ts", "build": "npm run build:client && npm run build:server", "build:client": "vite build", "build:server": "tsc -p server/tsconfig.json", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "start": "node dist/server/index.js"}, "keywords": ["email", "web", "typescript", "react", "nodejs"], "author": "Email Manager Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "zustand": "^4.4.7", "axios": "^1.6.2", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "dayjs": "^1.11.10", "socket.io-client": "^4.7.4"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "concurrently": "^8.2.2", "nodemon": "^3.0.2", "prettier": "^3.1.1"}, "serverDependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "pg": "^8.11.3", "redis": "^4.6.11", "node-imap": "^0.9.6", "nodemailer": "^6.9.7", "mailparser": "^3.6.5", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "winston": "^3.11.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.9", "@types/node-imap": "^0.9.4", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11"}}
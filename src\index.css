@import 'antd/dist/reset.css';

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  margin: 0 auto;
  text-align: left;
}

.ant-layout {
  background: #f5f5f5 !important;
}

.ant-layout-sider {
  background: #fff !important;
  border-right: 1px solid #f0f0f0;
}

.ant-layout-header {
  background: #fff !important;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 24px;
}

.email-list-item {
  cursor: pointer;
  transition: background-color 0.2s;
}

.email-list-item:hover {
  background-color: #f5f5f5;
}

.email-list-item.unread {
  font-weight: 600;
}

.email-content {
  max-height: 70vh;
  overflow-y: auto;
}

.compose-editor {
  min-height: 400px;
}